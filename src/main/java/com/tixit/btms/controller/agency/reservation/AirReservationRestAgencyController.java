package com.tixit.btms.controller.agency.reservation;

import com.tixit.btms.controller.agency.CommonAgencyWebController;
import com.tixit.btms.domain.common.Airport;
import com.tixit.btms.domain.company.Company;
import com.tixit.btms.domain.company.Department;
import com.tixit.btms.domain.constants.Constants;
import com.tixit.btms.domain.enumeration.BookingType;
import com.tixit.btms.domain.travel.*;
import com.tixit.btms.domain.user.TravelAgencyUser;
import com.tixit.btms.domain.user.User;
import com.tixit.btms.dto.reservation.AddTasfDTO;
import com.tixit.btms.dto.travel.BookingAirTravelerDTO;
import com.tixit.btms.exception.UserNotFoundException;
import com.tixit.btms.job.AirDailyReportJob;
import com.tixit.btms.job.BookingApprovalReminderAndCancelJob;
import com.tixit.btms.job.HyundaiCardTicketJob;
import com.tixit.btms.job.ISOSJob;
import com.tixit.btms.job.PRNAutoCancelJob;
import com.tixit.btms.repository.user.TravelAgencyUserRepository;
import com.tixit.btms.service.common.*;
import com.tixit.btms.service.payment.PaymentService;
import com.tixit.btms.service.travel.*;
import com.tixit.btms.service.user.CustomerService;
import com.tixit.btms.util.CommonUtil;
import com.tixit.btms.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 항공 예약내역
 * Created by icetiger on 2017-01-22.
 */
@Slf4j
@RestController
// @Profile({"agency", "hn-agency", "prod-agency"})
public class AirReservationRestAgencyController extends CommonAgencyWebController {
	
	@Autowired
	private TravelService travelService;
	
	@Autowired
	private PRNAutoCancelJob prnAutoCancelJob;
	
	@Autowired
	private ISOSJob isosJob;
	
	@Autowired
	private HyundaiCardTicketJob hyundaiTicketJob;

	@Autowired
	private BookingApprovalReminderAndCancelJob bookingApprovalReminderAndCancelJob;

	@Autowired
	private AirDailyReportJob airDailyReportJob;
	
	//세이버 동기화(Offline -> Online)
	@Value("#{'${sabre.pcc}'.split(',')}")
	private List<String> sabreReadPcc;
	
	@Value("#{'${sabre.queue}'.split(',')}")
	private List<String> sabreReadQ;

	@Autowired
	private CustomerService customerService;
	
	@Autowired
	private PaymentService paymentService;
	
	@Autowired
	private EmailServiceV2 emailServiceV2;
	
	@Autowired
	private AirportService airportService;
	
	@Value("${defaultDomain}")
	private String defaultDomain;
	
	@Autowired
	private BookingHistoryService bookingHistoryService;

	@Autowired
	private BookingAirTravelerService bookingAirTravelerService;
	
	@Autowired
	private KakaoTalkService kakaoTalkService;
	
	@Autowired
	private BookingAirService bookingAirService;
	
	@Autowired
	private TravelAgencyUserRepository travelAgencyUserRepository;
    @Autowired
    private DocumentNumberService documentNumberService;

	@RequestMapping(value = "/agency/reservation/air/saveProcess", method = RequestMethod.POST)
	public Map<String, Object> addProcess(Travel travel) throws Exception {
		
		Map<String, Object> returnMap = new HashMap<>();
		
		try {
			BookingAir bookingAir = travel.getBookingAir();
			User manager = travelAgencyUserRepository.getOne(bookingAir.getManager().getId());
			Department managerDepartment = manager.getDepartment();
			if (null != managerDepartment) {
				if (null != managerDepartment.getParentId()) {
					bookingAir.setManagerDepartment(managerDepartment.getParentId());
				} else {
					bookingAir.setManagerDepartment(managerDepartment.getId());
				}
			}
			// -------------------------------------------------------------------------------------------------------------
			// UserId 유효성 체크
			// -------------------------------------------------------------------------------------------------------------
			if (null != bookingAir && null != bookingAir.getBookingAirTravelers()) {
				for (BookingAirTraveler bookingAirTraveler : bookingAir.getBookingAirTravelers()) {
					if (null != bookingAirTraveler.getTraveler().getId() && bookingAirTraveler.getTraveler().getId().intValue() > 0) {
						int customerCnt = customerService.getCustomerCount(travel.getWorkspaceId(), bookingAirTraveler.getTraveler().getId());
						
						if (customerCnt == 0) {
							returnMap.put("resultCode", "NOT_EXIST_USER");
							return returnMap;
							
						} else {
							if (!CommonUtil.isNullOrSpace(bookingAirTraveler.getTravelerName())) {
								int customerCntWithName = customerService.getCustomerCount(travel.getWorkspaceId(), bookingAirTraveler.getTraveler().getId(), bookingAirTraveler.getTravelerName());
								
								if (customerCntWithName == 0) {
									returnMap.put("resultCode", "INVALID_USER");
									return returnMap;
								}
							}
						}
					}
				}
			}
			
			// 출장지 공항코드 세팅
			if (null != bookingAir && null != bookingAir.getBookingAirSchedules() && bookingAir.getBookingAirSchedules().size() > 0) {
				List<BookingAirSchedule> schedules = bookingAir.getBookingAirSchedules();
				BookingAirSchedule prevSchedule = null;
				Airport travelPlaceAirport = null;
				
				for (BookingAirSchedule schedule : schedules) {
					if (prevSchedule != null) {
						SimpleDateFormat format = new SimpleDateFormat("yyyy-mm-dd");
						Date firstDate = format.parse(prevSchedule.getFromYmd());
						Date secondDate = format.parse(schedule.getFromYmd());
						if ("OPEN".equals(schedule.getAirlineFlightNo()) || secondDate == null) {
						travelPlaceAirport = prevSchedule.getToAirport();
						break;
					} else {
						LocalDate departDateToCompare = firstDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
						LocalDate arriveDateToCompare = secondDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
						
						if (!departDateToCompare.isEqual(arriveDateToCompare) || (schedules.size() == 2 && prevSchedule.getFromAirport().getId().equals(schedule.getToAirport().getId()))) {
							//가는편/오는편 다른날이거나 , 당일왕복일땐 앞여정의 departure 공항을 travelPlaceAirport로 셋팅
							travelPlaceAirport = prevSchedule.getToAirport();
							break;
						} else if (departDateToCompare.isEqual(arriveDateToCompare)){
						//당일 환승 - 뒤 여정을 travelPlaceAirport로 셋팅
						travelPlaceAirport = schedule.getToAirport();
						}
					}
				} else {
					travelPlaceAirport = schedule.getToAirport();
				}
					prevSchedule = schedule;
				}
				
				travel.setTravelPlace(airportService.getOne(travelPlaceAirport.getId()).getCode());
			}
			
			travelService.saveTravel4Direct(travel);
			returnMap.put("resultCode", "SUCCESS");
			
		} catch (Exception e) {
			returnMap.put("resultCode", "FAIL");
			e.printStackTrace();
		}
		return returnMap;
	}

	/**
	 * 발권취소 완료처리
	 *
	 * @param travelId
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/agency/reservation/air/completeTicketCancelProcess", method = RequestMethod.POST)
	public Map<String, Object> completeTicketCancelAjax(Long travelId) throws Exception {
		travelService.completeTicketCancel(travelId);
		
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("success", true);
		
		
		return resultMap;
	}
	
	@RequestMapping(value = "/agency/reservation/air/autoCancelPnrJob", method = POST)
	public void runAutoCancelPnrJob() throws Exception {
		prnAutoCancelJob.run();
	}

	@RequestMapping(value = "/agency/reservation/air/airDailyReportJob", method = POST)
	public void runAirDailyReportJob() throws Exception {
		airDailyReportJob.run();
	}
	
	@RequestMapping(value = {"/agency/reservation/air/isosJob", "/agency/reservation/air/isosJob/{searchYmd}"}, method = RequestMethod.GET)
	public void isosJob(@PathVariable("searchYmd") Optional<String> searchYmd) throws Exception {
		
		if (searchYmd.isPresent()) {
			isosJob.setSearchTicketYmd(searchYmd.get());
		}
		isosJob.run();
	}
	
	@RequestMapping(value = {"/common/hyundaiTicketJob/{searchYmd}"}, method = RequestMethod.GET)
	public void hyundaiTicketJob(@PathVariable("searchYmd") Optional<String> searchYmd) throws Exception {
		
		if (searchYmd.isPresent()) {
			hyundaiTicketJob.setSearchTicketYmd(searchYmd.get());
		}
		hyundaiTicketJob.run();
	}

	@RequestMapping(value = "/agency/reservation/air/bookingApprovalReminderAndCancelJob", method = RequestMethod.GET)
	public void bookingApprovalReminderAndCancelJob() throws Exception {
		bookingApprovalReminderAndCancelJob.run();
	}
	
	@RequestMapping(value = "/agency/reservation/air/status/viewAjax")
	public Map<String, Object> viewAjax(Long travelId) {
		
		Map<String, Object> resultMap = new HashMap<>();
		
		Travel travel = travelService.getOne(travelId);
		
		if (Constants.STATUS_CODE_AIR_IS_BOOKING_DOING.intValue() == travel.getStatusCode().getId().intValue()
			|| Constants.STATUS_CODE_AIR_IS_BOOKING_COMPLETE.intValue() == travel.getStatusCode().getId().intValue())
		{
			resultMap.put("cancelStatusName", "예약취소");
			resultMap.put("cancelStatusCodeId", Constants.STATUS_CODE_AIR_IS_BOOKING_CANCEL);
		}
		return resultMap;
	}
	
	/**
	 * Tasf 수기 등록
	 */
	@RequestMapping(value = "/agency/reservation/air/addTasf", method = POST)
	public ResponseEntity addTasf(@RequestBody AddTasfDTO addTasfDTO){
		
		TravelAgencyUser agencyUser = travelService.getTravelAgencyUser(Long.valueOf(addTasfDTO.getAgencyUserId()));
		if(agencyUser == null){
			String msg = "Not found userid=" + addTasfDTO.getAgencyUserId() + " from TravelAgencyUser table.";
			log.error(msg);
			throw new UserNotFoundException(msg);
		}
		try {
			paymentService.createPaymentTasf(addTasfDTO, agencyUser);
			return ResponseEntity.ok().build();
		}catch(Exception e){
			log.error("An error occurred while processing addTasf.", e);
			throw e;
		}
	}
	
	/**
	 * 발권 완료 메일 버튼
	 * kloto 210527 추가
	 *
	 * @param travelId
	 * @throws Exception
	 */
	@RequestMapping(value = "/agency/reservation/air/status/completeMailSend")
	public void ticketCompleteMailSend(Long travelId) throws Exception {
		Travel travel = travelService.getOne(travelId);
		Company company = travel.getCompany();
		boolean isAdmin;
		if ((null != company.getAirEmSetting()) && company.getAirEmSetting().isCompleteFront()) {
			isAdmin = false;
			emailServiceV2.sendEticketIssue(travel, isAdmin);
			kakaoTalkService.sendTicketComplete(travel);
		}
		if ((null != company.getAirEmSetting()) && company.getAirEmSetting().isCompleteAdmin()) {
			isAdmin = true;
			emailServiceV2.sendEticketIssue(travel, isAdmin);
		}
	}
	
	@RequestMapping(value = "/agency/reservation/air/updateViewTicketDate", method = RequestMethod.POST)
	public void updateViewTicketDate(long travelId,
	                             long viewTicketDateOrigin,
	                             long viewTicketDate) throws Exception {
		String viewTicketDateOriginString = DateUtil.date2String(new Date(viewTicketDateOrigin), "yyyy-MM-dd HH:mm:ss");
		String viewTicketDateString = DateUtil.date2String(new Date(viewTicketDate), "yyyy-MM-dd HH:mm:ss");
		travelService.viewTicketDateModify(travelId, viewTicketDateOriginString, viewTicketDateString);
	}
	
	@RequestMapping(value = "/agency/reservation/air/updateTicketDate", method = RequestMethod.POST)
	public void updateTicketDate(long travelId,
								 long ticketDateOrigin,
								 long ticketDate
								 ) throws Exception {
		String ticketDateOriginString = DateUtil.date2String(new Date(ticketDateOrigin), "yyyy-MM-dd HH:mm:ss");
		String ticketDateString = DateUtil.date2String(new Date(ticketDate), "yyyy-MM-dd HH:mm:ss");
		travelService.ticketDateModify(travelId, ticketDateOriginString, ticketDateString);
	}
	
	@RequestMapping(value = "/agency/reservation/air/modify-booking-date", method = RequestMethod.POST)
	public void modifyBookingDate(long travelId,
								 long bookingDateOrigin,
								 long bookingDate) throws Exception {
		String ticketDateOriginString = DateUtil.date2String(new Date(bookingDateOrigin), "yyyy-MM-dd HH:mm:ss");
		String ticketDateString = DateUtil.date2String(new Date(bookingDate), "yyyy-MM-dd HH:mm:ss");							
		travelService.bookingDateModify(travelId, ticketDateOriginString, ticketDateString);
	}
	
	@RequestMapping(value = "/agency/reservation/air/updateGuessAmount", method = RequestMethod.POST)
	public void updateGuessAmount(long travelId,
	                              String guessAmount) throws Exception {
		travelService.guessAmountModify(travelId, guessAmount);
	}

	/**
	 * 행사 번호를 그룹과 하위 티켓에 모두 맵핑
	 * @param travelId
	 * @param groupNumber
	 * @throws Exception
	 */
	@RequestMapping(value = "/agency/reservation/air/groupNumber", method = RequestMethod.POST)
	public void updateGroupNumber(long travelId, String groupNumber) throws Exception {
		travelService.modifyGroupNumber(travelId, groupNumber);
	}

	@RequestMapping(value = "/agency/reservation/air/removeProcess", method = RequestMethod.POST)
	public boolean removeProcess(Long travelId) throws Exception {
		
		if (null != travelId) {
			Travel travel = travelService.getOne(travelId);
			travel.setIsDeleted(true);
			
			travelService.save(travel);
			
			//BookingHistory DIRECT 정보 삭제 저장
			bookingHistoryService.saveBookingHistory(travel, "ReservationIsDeleted", "예약정보", "", "삭제");
		}
		return true;
	}
	
	@RequestMapping(value = "/agency/reservation/air/bookingHistory/listAjax")
	public List<BookingHistory> bookingHistoryListAjax(@RequestParam Long travelId) {
		
		List<BookingHistory> bookingHistories = bookingHistoryService.getListByTravelId(travelId);
		
		return bookingHistories;
	}

	@RequestMapping(value = "/agency/reservation/air/updateBookingFare", method = RequestMethod.POST)
	public Map<String, Object> updateBookingFare(@RequestBody BookingAirTravelerFare[] bookingAirTravelerFares) throws Exception {

		Map<String, Object> resultMap = new HashMap<>();
		try {
			bookingAirTravelerService.modifyBookingFare(bookingAirTravelerFares, CommonUtil.getLoginAgencyUser());
			resultMap.put("resultCode", "SUCCESS");
		} catch (Exception e) {
			log.error(e.getMessage());
			resultMap.put("resultCode", "FAIL");
		}
		return resultMap;
	}
	
	@RequestMapping(value = "/agency/reservation/air/updateTravelPlace", method = RequestMethod.POST)
	public void updateTravelPlace(long travelId,
								  String airportCode) throws Exception {
		travelService.updateTravelPlace(travelId, airportCode);
	}
	
	@RequestMapping(value = "/agency/reservation/travel/air/modifyBookingAirTraveler", method = RequestMethod.POST)
	public Map<String, Object> modifyPassengerPassportProcess(BookingAirTravelerDTO bookingAirTraveler) throws Exception {
		Map<String, Object> resultMap = new HashMap<>();
		
		try {
			Long bookingAirTravelerId = bookingAirTraveler.getId();
			BookingAirTraveler origin = bookingAirTravelerService.getOne(bookingAirTravelerId);
			
			if (origin != null) {
				bookingAirTravelerService.modifyTravelerMileageInfos(bookingAirTraveler);
				
				resultMap.put("bookingAirTraveler", origin);
				resultMap.put("resultCode", "SUCCESS");
			}
		} catch (Exception e) {
			log.error("*** modifyPassengerPassportProcess : " + e);
			e.printStackTrace();
			resultMap.put("resultCode", "FAIL");
		}
		return resultMap;
	}
	
	@RequestMapping(value = "/agency/reservation/travel/air/modifyPassport", method = RequestMethod.POST)
	public Map<String, Object> modifyPassportNumber(@RequestBody BookingAirTravelerDTO bookingAirTravelerDto) {
		Map<String, Object> resultMap = new HashMap<>();
		try {
			bookingAirTravelerService.modifyTravelerPassport(bookingAirTravelerDto);
//			travelService.updatePnrAPISInfo(bookingAirTravelerDto.getTravelId());
			resultMap.put("resultCode", "SUCCESS");
		} catch (Exception e) {
			log.error("*** modifyPassport : " + e);
			e.printStackTrace();
			resultMap.put("resultCode", "FAIL");
		}
		return resultMap;
	}

	@RequestMapping(value = "/agency/reservation/air/view/{travelId}/resetIsApisUpdateFailed", method = RequestMethod.POST)
	public Map<String, Object> resetIsApisUpdateFailed(@PathVariable("travelId") Long travelId) throws Exception {
		Map<String, Object> resultMap = new HashMap<>();

		try {
			Travel travel = travelService.getTravel4BookingComplete(travelService.getOne(travelId));
			if (travel.getBookingAir().getIsApisUpdateFailed() != null && travel.getBookingAir().getIsApisUpdateFailed()) {
				bookingAirService.updateIsApisUpdateFailed(travel.getBookingAir(), false);
			}
			resultMap.put("resultCode", "SUCCESS");
		} catch (Exception e) {
			log.error("*** modifyPassport : " + e);
			resultMap.put("resultCode", "FAIL");
		}
		return resultMap;
	}

	@RequestMapping(value = "/agency/reservation/air/status/modify-ticket-status")
	public void modifyTicketStatus(Long travelId,
							  String ticketStatus) throws Exception {
		
		Travel travel = travelService.getOne(travelId);
		
		travelService.statusTicketStatusModify(travel, ticketStatus);
		
	}
	
	@RequestMapping(value = "/agency/reservation/air/status/modify-payment-status")
	public void modifyPaymentStatus(Long travelId,
									String paymentStatus) throws Exception {
		
		Travel travel = travelService.getOne(travelId);
		
		travelService.statusPaymentStatusModify(travel, paymentStatus);
		
	}

	@RequestMapping(value = "/agency/reservation/air/modifyDocumentNumber", method = RequestMethod.POST)
	public Map<String, Object> modifyDocumentNumber(@RequestBody HashMap<String, Object> map) throws Exception {
		Map<String, Object> resultMap = new HashMap<>();
		try {
			log.info("modifyDocumentNumber map : {}", map);
			Object numbers = map.get("documentNumberList");
			documentNumberService.modifyDocumentNumber(Long.valueOf((String) map.get("travelId")), (ArrayList) numbers, BookingType.AIR);
			resultMap.put("resultCode", "SUCCESS");
		} catch (Exception e) {
			log.error("*** modifyDocumentNumber : ", e);
			resultMap.put("resultCode", "FAIL");
		}
		return resultMap;
	}
}
