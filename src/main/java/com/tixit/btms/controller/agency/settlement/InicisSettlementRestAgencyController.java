package com.tixit.btms.controller.agency.settlement;

import com.tixit.btms.controller.agency.CommonAgencyWebController;
import com.tixit.btms.dto.paging.PagingDTO;
import com.tixit.btms.dto.settlement.InicisSettlementSearchDTO;
import com.tixit.btms.dto.settlement.InicisSettlementDTO;
import com.tixit.btms.job.InicisSettlementJob;
import com.tixit.btms.service.settlement.InicisSettlementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.web.bind.annotation.RequestMethod.GET;

/**
 * 이니시스 승인/정산대사 REST Agency Controller
 *
 * <AUTHOR>
 * @date    2020.03.19
 */
@Slf4j
@RestController
// @Profile({"agency", "hn-agency", "prod-agency"})
public class InicisSettlementRestAgencyController extends CommonAgencyWebController {

	@Autowired
	private InicisSettlementService inicisSettlementService;

	@Autowired
	private InicisSettlementJob inicisSettlementJob;

	@RequestMapping(value = {"/agency/settlement/inicis/listAjax"})
	public Map<String, Object> listAjax(InicisSettlementSearchDTO searchDTO,
										PagingDTO pagingDTO) throws Exception {

		int totalCount = inicisSettlementService.getTotalCount(searchDTO);

		List<InicisSettlementDTO> list = null;

		if (totalCount > 0) {
			list = inicisSettlementService.getList(searchDTO);
		}

		Map<String, Object> returnMap = new HashMap<>();
		returnMap.put("list", list);

		return returnMap;
	}

	@RequestMapping(value = {"/agency/settlement/inicis/testAjax"})
	public String testAjax(String actionUrl) throws Exception {

		String data = null;
		log.debug("*** searchDTO : " + actionUrl);

		log.debug("이니시스 정산대사 Parameter = {}", actionUrl);

		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse httpResponse = null;

		try {
			HttpGet request = new HttpGet(actionUrl);
			request.setHeader("charset", "UTF-8");

			httpResponse = httpClient.execute(request);
			log.debug("이니시스 정산대사 response Status = {}", httpResponse.getStatusLine().getStatusCode());

			data = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
			log.debug("이니시스 승인대사 response data = {}" + data);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage(), e);

		} finally {
			httpClient.close();
			if (httpResponse != null) {
				httpResponse.close();
			}
		}

		return data;
	}

	/**
	 * 이니시스 정산대사 데이타 전송 JOB 강제 실행.
	 *
	 * @throws Exception the exceptio n
	 */
	@RequestMapping(value = "/agency/settlement/inicis/runInicisSettlementJob/{searchYmd}", method = GET)
	public void runInicisSettlementJob(@PathVariable("searchYmd") String searchYmd) throws Exception {

		inicisSettlementJob.callInicisSettlement(searchYmd);
	}
}
