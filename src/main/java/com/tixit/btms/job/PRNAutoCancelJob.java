package com.tixit.btms.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.tixit.btms.domain.enumeration.TravelBookingType;
import com.tixit.btms.domain.travel.Travel;
import com.tixit.btms.dto.travel.TravelStatusHistoryDTO;
import com.tixit.btms.domain.code.Code;
import com.tixit.btms.domain.constants.Constants;
import com.tixit.btms.repository.codemanage.CodeRepository;
import com.tixit.btms.repository.travel.TravelRepository;
import com.tixit.btms.repository.travel.TravelStatusHistory4Mybatis;
import com.tixit.btms.util.DateUtil;

import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

@Slf4j
@Component("prnAutoCancelJob")
// @Profile({"agency", "hn-agency", "prod-agency"})
public class PRNAutoCancelJob {

  @Autowired
  private TravelRepository travelRepository;

  @Autowired
  private TravelStatusHistory4Mybatis travelStatusHistory4Mybatis;

  @Autowired
  private CodeRepository codeRepository;

  private final static boolean jobServer;
  static{
    jobServer = Boolean.parseBoolean(System.getProperty("job.scheduler"));
  }
  
  public void run(){
    if(jobServer == false) {
      return;
    }

    log.info("### PRNAutoCancelJob 시작 ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));

    Code cancelStatusCode = this.codeRepository.getOne(Constants.STATUS_CODE_AIR_IS_BOOKING_CANCEL);
    while (true) {
      List<Travel> travels = travelRepository.findExpiredTravel(1000);
      for(Travel travel : travels) {
        this.travelStatusHistory4Mybatis.insert(new TravelStatusHistoryDTO() {{
          setTravelId(travel.getId());
          setModifierUserId(null);
          setModifyDate(new Date());
          setModifyInfo("취소 처리 되었습니다.");
          setStatusCodeId(Constants.STATUS_CODE_AIR_IS_BOOKING_CANCEL);
          setTravelBookingType(TravelBookingType.Air.getCode());
        }});
        
        travel.setStatusCode(cancelStatusCode);
        travelRepository.save(travel);
      }
      
      if (travels.size() < 1000) {
        break;
      }
    }
    
		log.info("### PRNAutoCancelJob 완료 ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));
  }
}
