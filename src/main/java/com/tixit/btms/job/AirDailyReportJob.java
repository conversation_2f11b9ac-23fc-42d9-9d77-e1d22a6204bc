package com.tixit.btms.job;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// import javax.annotation.PostConstruct;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.tixit.btms.repository.report.AirDailyReportRepository4Mybatis;
import com.tixit.btms.service.microsoft.MicrosoftService;
import com.tixit.btms.util.DateUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("airDailyReportJob")
// @Profile({"agency", "hn-agency", "prod-agency"})
public class AirDailyReportJob {

  @Autowired
  private AirDailyReportRepository4Mybatis airDailyReportRepository4Mybatis;

  @Autowired
  private MicrosoftService microsoftService;

  @Value("${upload.tempdir}")
  private String uploadServerDirectory;

	@Value("${btms.env.type}")
  private String btmsEnvType;

  private final static boolean jobServer;
  static {
    jobServer = Boolean.parseBoolean(System.getProperty("job.scheduler"));
  }

  // @PostConstruct
  // public void init() {
  //   run1();
  // }

  public void run() {
    if (jobServer == false) {
      return;
    }

    this.run1();
  }

  private void run1() {
    log.info("### AirDailyReportJob 시작 ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));

    String yesterday = DateUtil.date2String(DateUtil.plusDays(new Date(), -1), "yyyyMMdd");
    String year = yesterday.substring(0, 4);

    File overseasFile = null;
    File domesticFile = null;
    try {
      overseasFile = this.buildFileOverseas(yesterday);
      domesticFile = this.buildFileDomestic(yesterday);
    } catch (Exception e) {
      log.error("### AirDailyReportJob Error ### : " + e.getMessage());
      return;
    }

    if (btmsEnvType.equals("prod")) {
      try {
        String accessToken = this.microsoftService.getToken();
        Map<String, Object> body = new HashMap<>();
        body.put("saveToSentItems", true);
        Map<String, Object> message = new HashMap<>();
        message.put("subject", "[ 상용운영팀 _ 국제선/국내선 매출보고서 ]  "
            + DateUtil.date2String(DateUtil.plusDays(new Date(), -1), "yyyy/MM/dd") + "  국내선/국제선  DSR 송부");
        Map<String, Object> messageBody = new HashMap<>();
        messageBody.put("contentType", "HTML");
        messageBody.put("content", "<p>상용운영팀 국제선/국내선 매출 보고서</p>");
        message.put("body", messageBody);
        String[] tos = { "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };
        List<Map<String, Object>> toRecipients = new ArrayList<>();
        for (String to : tos) {
          Map<String, Object> toRecipient = new HashMap<>();
          toRecipient.put("emailAddress", new HashMap<String, String>() {
            {
              put("address", to);
            }
          });
          toRecipients.add(toRecipient);
        }
        message.put("toRecipients", toRecipients);
        String[] ccs = { "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };
        List<Map<String, Object>> ccRecipients = new ArrayList<>();
        for (String cc : ccs) {
          Map<String, Object> ccRecipient = new HashMap<>();
          ccRecipient.put("emailAddress", new HashMap<String, String>() {
            {
              put("address", cc);
            }
          });
          ccRecipients.add(ccRecipient);
        }
        message.put("ccRecipients", ccRecipients);
        List<Map<String, Object>> attachments = new ArrayList<>();
        Map<String, Object> attachmentOverseas = new HashMap<>();
        attachmentOverseas.put("@odata.type", "#microsoft.graph.fileAttachment");
        attachmentOverseas.put("name", "국제선 Daily report-" + year + "년.xlsx");
        attachmentOverseas.put("contentType", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        attachmentOverseas.put("contentBytes", new String(Base64.getEncoder().encode(Files.readAllBytes(overseasFile.toPath()))));
        attachments.add(attachmentOverseas);
        Map<String, Object> attachmentDomestic = new HashMap<>();
        attachmentDomestic.put("@odata.type", "#microsoft.graph.fileAttachment");
        attachmentDomestic.put("name", "국내선 Daily report-" + year + "년.xlsx");
        attachmentDomestic.put("contentType", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        attachmentDomestic.put("contentBytes", new String(Base64.getEncoder().encode(Files.readAllBytes(domesticFile.toPath()))));
        attachments.add(attachmentDomestic);
        message.put("attachments", attachments);
        body.put("message", message);
        this.microsoftService.sendEmail(accessToken, body);
      } catch (Exception e) {
        log.error("### AirDailyReportJob Error ### : " + e.getMessage());
      }
    }

    log.info("### AirDailyReportJob 완료 ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));
  }

  private File buildFileOverseas(String yesterday) throws Exception {
    String year = yesterday.substring(0, 4);
    String month = yesterday.substring(4, 6);
    int day = Integer.parseInt(yesterday.substring(6, 8));
    File file = new File(uploadServerDirectory + "/form/Overseas_Air_Daily_Report.xlsx");
    Map<String, Object> result = airDailyReportRepository4Mybatis.selectOverseasAirDailyReport(yesterday);
    BigDecimal totalFare = result == null ? BigDecimal.ZERO : (BigDecimal) result.get("TOTAL_FARE");
    BigDecimal totalTax = result == null ? BigDecimal.ZERO : (BigDecimal) result.get("TOTAL_TAX");
    BigDecimal totalTasf = result == null ? BigDecimal.ZERO : (BigDecimal) result.get("TOTAL_TASF");
    BigDecimal totalRefund = result == null ? BigDecimal.ZERO : (BigDecimal) result.get("TOTAL_REFUND");
    BigDecimal ticketCount = result == null ? BigDecimal.ZERO : (BigDecimal) result.get("TICKET_COUNT");

    // Bước 1: Mở file để đọc
    Workbook workbook;
    try (FileInputStream fis = new FileInputStream(file)) {
      workbook = new XSSFWorkbook(fis);
    } catch (Exception e) {
      throw e;
    }
    // Bước 3: Ghi lại vào chính file đó
    String sheetName = "취급액" + year + "(투어비스 " + month + ")";
    Sheet sheet = workbook.getSheet(sheetName);
    if (sheet == null) {
      sheet = workbook.cloneSheet(workbook.getSheetIndex("Template"));
      workbook.setSheetName(workbook.getSheetIndex(sheet), sheetName);
      for (int i = 1; i <= 31; i++) {
        try {
          LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), i);
          sheet.getRow(i + 4).getCell(0).setCellValue(month + "/" + i);
        } catch (DateTimeException e) {
          sheet.removeRow(sheet.getRow(i + 4));
        }
      }
    }
    sheet.getRow(day + 4).getCell(13).setCellValue(totalRefund.doubleValue());
    sheet.getRow(day + 4).getCell(16).setCellValue(totalFare.doubleValue());
    sheet.getRow(day + 4).getCell(17).setCellValue(totalTax.doubleValue());
    sheet.getRow(day + 4).getCell(18).setCellValue(totalTasf.doubleValue());
    sheet.getRow(day + 4).getCell(19).setCellValue(ticketCount.doubleValue());

    FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
    Cell cell15 = sheet.getRow(day + 4).getCell(15);
    cell15.setCellValue(evaluator.evaluate(cell15).getNumberValue());
    Cell cell14 = sheet.getRow(day + 4).getCell(14);
    cell14.setCellValue(evaluator.evaluate(cell14).getNumberValue());
    Cell cell6 = sheet.getRow(day + 4).getCell(6);
    cell6.setCellValue(evaluator.evaluate(cell6).getNumberValue());
    Cell cell5 = sheet.getRow(day + 4).getCell(5);
    cell5.setCellValue(evaluator.evaluate(cell5).getNumberValue());
    Cell cell4 = sheet.getRow(day + 4).getCell(4);
    cell4.setCellValue(evaluator.evaluate(cell4).getNumberValue());
    Cell cell3 = sheet.getRow(day + 4).getCell(3);
    cell3.setCellValue(evaluator.evaluate(cell3).getNumberValue());
    Cell cell2 = sheet.getRow(day + 4).getCell(2);
    cell2.setCellValue(evaluator.evaluate(cell2).getNumberValue());
    Cell cell1 = sheet.getRow(day + 4).getCell(1);
    cell1.setCellValue(evaluator.evaluate(cell1).getNumberValue());

    Cell ttlCell1 = sheet.getRow(4).getCell(1);
    ttlCell1.setCellValue(evaluator.evaluate(ttlCell1).getNumberValue());
    Cell ttlCell2 = sheet.getRow(4).getCell(2);
    ttlCell2.setCellValue(evaluator.evaluate(ttlCell2).getNumberValue());
    Cell ttlCell3 = sheet.getRow(4).getCell(3);
    ttlCell3.setCellValue(evaluator.evaluate(ttlCell1).getNumberValue());
    Cell ttlCell4 = sheet.getRow(4).getCell(4);
    ttlCell4.setCellValue(evaluator.evaluate(ttlCell4).getNumberValue());
    Cell ttlCell5 = sheet.getRow(4).getCell(5);
    ttlCell5.setCellValue(evaluator.evaluate(ttlCell5).getNumberValue());
    Cell ttlCell6 = sheet.getRow(4).getCell(6);
    ttlCell6.setCellValue(evaluator.evaluate(ttlCell6).getNumberValue());
    Cell ttlCell13 = sheet.getRow(4).getCell(13);
    ttlCell13.setCellValue(evaluator.evaluate(ttlCell13).getNumberValue());
    Cell ttlCell14 = sheet.getRow(4).getCell(14);
    ttlCell14.setCellValue(evaluator.evaluate(ttlCell14).getNumberValue());
    Cell ttlCell15 = sheet.getRow(4).getCell(15);
    ttlCell15.setCellValue(evaluator.evaluate(ttlCell15).getNumberValue());
    Cell ttlCell16 = sheet.getRow(4).getCell(16);
    ttlCell16.setCellValue(evaluator.evaluate(ttlCell16).getNumberValue());
    Cell ttlCell17 = sheet.getRow(4).getCell(17);
    ttlCell17.setCellValue(evaluator.evaluate(ttlCell17).getNumberValue());
    Cell ttlCell18 = sheet.getRow(4).getCell(18);
    ttlCell18.setCellValue(evaluator.evaluate(ttlCell18).getNumberValue());
    Cell ttlCell19 = sheet.getRow(4).getCell(19);
    ttlCell19.setCellValue(evaluator.evaluate(ttlCell19).getNumberValue());

    workbook.setActiveSheet(workbook.getSheetIndex(sheet));
    workbook.setSelectedTab(workbook.getSheetIndex(sheet));

    try (FileOutputStream fos = new FileOutputStream(file)) {
      workbook.write(fos);
    } catch (Exception e) {
      throw e;
    } finally {
      try {
        if (workbook != null) {
          workbook.close();
        }
      } catch (IOException e) {
      }
    }

    return file;
  }

  private File buildFileDomestic(String yesterday) throws Exception {
    String year = yesterday.substring(0, 4);
    String month = yesterday.substring(4, 6);
    int day = Integer.parseInt(yesterday.substring(6, 8));
    File file = new File(uploadServerDirectory + "/form/Domestic_Air_Daily_Report.xlsx");
    Map<String, Object> result = airDailyReportRepository4Mybatis.selectDomesticAirDailyReport(yesterday);
    if (result == null) {
      log.info("### AirDailyReportJob Domestic Result ### : No Data");
      return file;
    }
    Double net = (Double) result.get("DIFF_NET_AMOUNT");
    Double tax = (Double) result.get("DIFF_TAX_AMOUNT");
    BigDecimal ticketCount = (BigDecimal) result.get("TICKET_TYPE_TICKET_COUNT");

    // Bước 1: Mở file để đọc
    Workbook workbook;
    try (FileInputStream fis = new FileInputStream(file)) {
      workbook = new XSSFWorkbook(fis);
    } catch (Exception e) {
      throw e;
    }
    // Bước 3: Ghi lại vào chính file đó
    String sheetName = "취급액" + year + "(투어비스 " + month + ")";
    Sheet sheet = workbook.getSheet(sheetName);
    if (sheet == null) {
      sheet = workbook.cloneSheet(workbook.getSheetIndex("Template"));
      workbook.setSheetName(workbook.getSheetIndex(sheet), sheetName);
      for (int i = 1; i <= 31; i++) {
        try {
          LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), i);
          sheet.getRow(i + 4).getCell(0).setCellValue(month + "/" + i);
        } catch (DateTimeException e) {
          sheet.removeRow(sheet.getRow(i + 4));
        }
      }
    }
    sheet.getRow(day + 4).getCell(9).setCellValue(net.doubleValue());
    sheet.getRow(day + 4).getCell(10).setCellValue(tax.doubleValue());
    sheet.getRow(day + 4).getCell(12).setCellValue(ticketCount.doubleValue());

    FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
    Cell cell8 = sheet.getRow(day + 4).getCell(8);
    cell8.setCellValue(evaluator.evaluate(cell8).getNumberValue());
    Cell cell7 = sheet.getRow(day + 4).getCell(7);
    cell7.setCellValue(evaluator.evaluate(cell7).getNumberValue());
    Cell cell6 = sheet.getRow(day + 4).getCell(6);
    cell6.setCellValue(evaluator.evaluate(cell6).getNumberValue());
    Cell cell5 = sheet.getRow(day + 4).getCell(5);
    cell5.setCellValue(evaluator.evaluate(cell5).getNumberValue());
    Cell cell4 = sheet.getRow(day + 4).getCell(4);
    cell4.setCellValue(evaluator.evaluate(cell4).getNumberValue());
    Cell cell3 = sheet.getRow(day + 4).getCell(3);
    cell3.setCellValue(evaluator.evaluate(cell3).getNumberValue());
    Cell cell2 = sheet.getRow(day + 4).getCell(2);
    cell2.setCellValue(evaluator.evaluate(cell2).getNumberValue());
    Cell cell1 = sheet.getRow(day + 4).getCell(1);
    cell1.setCellValue(evaluator.evaluate(cell1).getNumberValue());

    Cell ttlCell1 = sheet.getRow(4).getCell(1);
    ttlCell1.setCellValue(evaluator.evaluate(ttlCell1).getNumberValue());
    Cell ttlCell2 = sheet.getRow(4).getCell(2);
    ttlCell2.setCellValue(evaluator.evaluate(ttlCell2).getNumberValue());
    Cell ttlCell3 = sheet.getRow(4).getCell(3);
    ttlCell3.setCellValue(evaluator.evaluate(ttlCell1).getNumberValue());
    Cell ttlCell4 = sheet.getRow(4).getCell(4);
    ttlCell4.setCellValue(evaluator.evaluate(ttlCell4).getNumberValue());
    Cell ttlCell5 = sheet.getRow(4).getCell(5);
    ttlCell5.setCellValue(evaluator.evaluate(ttlCell5).getNumberValue());
    Cell ttlCell6 = sheet.getRow(4).getCell(6);
    ttlCell6.setCellValue(evaluator.evaluate(ttlCell6).getNumberValue());
    Cell ttlCell7 = sheet.getRow(4).getCell(7);
    ttlCell7.setCellValue(evaluator.evaluate(ttlCell7).getNumberValue());
    Cell ttlCell8 = sheet.getRow(4).getCell(8);
    ttlCell8.setCellValue(evaluator.evaluate(ttlCell8).getNumberValue());
    Cell ttlCell9 = sheet.getRow(4).getCell(9);
    ttlCell9.setCellValue(evaluator.evaluate(ttlCell9).getNumberValue());
    Cell ttlCell10 = sheet.getRow(4).getCell(10);
    ttlCell10.setCellValue(evaluator.evaluate(ttlCell10).getNumberValue());
    Cell ttlCell12 = sheet.getRow(4).getCell(12);
    ttlCell12.setCellValue(evaluator.evaluate(ttlCell12).getNumberValue());

    workbook.setActiveSheet(workbook.getSheetIndex(sheet));
    workbook.setSelectedTab(workbook.getSheetIndex(sheet));

    try (FileOutputStream fos = new FileOutputStream(file)) {
      workbook.write(fos);
    } catch (Exception e) {
      throw e;
    } finally {
      try {
        if (workbook != null) {
          workbook.close();
        }
      } catch (IOException e) {
      }
    }

    return file;
  }
}
