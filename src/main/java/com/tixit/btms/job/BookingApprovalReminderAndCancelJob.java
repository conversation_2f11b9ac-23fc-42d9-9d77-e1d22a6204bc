package com.tixit.btms.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.tixit.btms.commoditization.card.dto.UsePaymentCardsResponseDTO;
import com.tixit.btms.commoditization.card.service.UsePaymentCardsService;
import com.tixit.btms.commoditization.entity.PaymentCardItem;
import com.tixit.btms.domain.company.Company;
import com.tixit.btms.domain.constants.Constants;
import com.tixit.btms.domain.enumeration.ManagerType;
import com.tixit.btms.domain.user.User;
import com.tixit.btms.dto.hotel.BookingDetailInfoResponse;
import com.tixit.btms.dto.hotel.BookingHotelDTO;
import com.tixit.btms.dto.payment.PaymentDTO;
import com.tixit.btms.dto.user.UserDTO;
import com.tixit.btms.repository.hotel.BookingHotelRepository4Mybatis;
import com.tixit.btms.service.common.EmailServiceV2;
import com.tixit.btms.service.company.CompanyService;
import com.tixit.btms.service.hotel.BookingHotelService;
import com.tixit.btms.service.payment.HotelPaymentService;
import com.tixit.btms.service.user.CustomerService;
import com.tixit.btms.util.DateUtil;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component("bookingApprovalReminderAndCancelJob")
// @Profile({"agency", "hn-agency", "prod-agency"})
public class BookingApprovalReminderAndCancelJob {

  @Autowired
  private BookingHotelRepository4Mybatis bookingHotelRepository4Mybatis;
  
  @Autowired
  private EmailServiceV2 emailServiceV2;

  @Autowired
	private CustomerService customerService;

  @Autowired
  private BookingHotelService bookingHotelService;

  @Autowired
  private CompanyService companyService;

	@Autowired
	private UsePaymentCardsService usePaymentCardsService;

  @Autowired
  private HotelPaymentService hotelPaymentService;

  private final static boolean jobServer;
  static{
    jobServer = Boolean.parseBoolean(System.getProperty("job.scheduler"));
  }
  
  public void run(){
     if(jobServer == false) {
      return;
    }

    log.info("### BookingApprovalReminderAndCancelJob 시작 ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));

    // 오늘 날짜를 yyyyMMdd 형식으로 가져오기
    String today = DateUtil.getFormatString("yyyyMMdd");
    
    // Integer.parseInt(today)
    try {
      // Status code 497 (품의) 이면서 cancel deadline이 오늘인 booking hotel 목록 조회
      List<BookingHotelDTO> pendingBookings = bookingHotelRepository4Mybatis.selectBookingsByStatusAndCancelDeadline(
          Constants.STATUS_CODE_HOTEL_IS_BOOKING_DOING, // 497 - 품의
          Integer.parseInt(today)
      );

      // 현재 시간 가져오기 (여러 방법)
      String currentHour = DateUtil.getFormatString("HH");        // 현재 시간(24시간) (예: "15")
      int currentHourInt = Integer.parseInt(currentHour);         // 현재 시간을 정수로
      
      // currentHourInt == 16 cancel booking hotel
       if (currentHourInt == 16) {
        for (BookingHotelDTO booking : pendingBookings) {
          Company company = companyService.getOne(booking.getCompanyId());
          if (!company.isAutoPayment() || company.getAutoPaymentCardId() == null) {
            bookingHotelService.freeCancelBookingHotel(booking.getBookingHotelId(), "0.0.0.1", false, true, null);
            continue;
          }
          UsePaymentCardsResponseDTO usePaymentCards = this.usePaymentCardsService.getCard(company.getAutoPaymentCardId());
          if (usePaymentCards == null) {
            bookingHotelService.freeCancelBookingHotel(booking.getBookingHotelId(), "0.0.0.1", false, true, null);
            continue;
          }

          booking.setCardId(usePaymentCards.getId());
          booking.setCardInfoByCardType(usePaymentCards, PaymentCardItem.CC, company.getName());
				  booking.setCorporateCard(true);

          try {
            PaymentDTO paymentDto = hotelPaymentService.doFormPayment(booking, "0.0.0.1");
            String resultCode = paymentDto.getInicisFormpayResponse().getResultCode();
            if (!"00".equals(resultCode)) {
              throw new Exception("결제 실패");
            }
            hotelPaymentService.sendInicisApplyData(paymentDto);
          } catch (Exception e) {
            // send email to agency
            this.bookingHotelService.sendAutoPaymentFailMail(booking);
            continue;
          }

          booking.setIsApproval(true);
          booking.setStatusCodeId(Constants.STATUS_CODE_HOTEL_IS_BOOKING_COMPLETE);
          booking.setApprovalMemo("");
          booking.setApprovalUserId(null);
          this.bookingHotelService.updateApproval(booking);

          // send email to agency, user, admin
          this.bookingHotelService.sendAutoPaymentMail(booking);
        }
        log.info("### autoCancelBookingHotel: {} ###", pendingBookings.size());
        return;
      }
      
      if (!pendingBookings.isEmpty()) {
        for (BookingHotelDTO booking : pendingBookings) {
          // 각 예약에 대해 경고 처리
          sendWarningEmail(booking);
        }
        log.info("### requestApprovalBookingHotel: {} ###", pendingBookings.size());
      }
      
    } catch (Exception e) {
      log.error("### BookingApprovalReminderAndCancelJob 실행 중 오류 발생 ###", e);
    }
    
		log.info("### BookingApprovalReminderAndCancelJob 완료 ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));
  }
  
  /**
   * 경고 이메일 발송 (필요시 구현)
   * @param booking 경고 대상 예약
   */
  private void sendWarningEmail(BookingHotelDTO booking) {
    Company company = companyService.getOne(booking.getCompanyId());
    if (!company.getAirEmSetting().isReservAdmin() && !company.getAirEmSetting().isReservAgency()) {
      return;
    }

    BookingDetailInfoResponse bookingDetailInfoResponse = null;
    if (booking != null) {
      try {
        bookingDetailInfoResponse = bookingHotelService.getWhiteLabelBookingDetail(booking.getBookingId());
      } catch (Exception e) {
        log.error("*** getWhiteLabelBookingDetail Error : "+ e.getMessage());
      }
    }

    if (company.getAirEmSetting().isReservAdmin()) {
      List<UserDTO> adminList = null;
      List<User> adminUserList = customerService.getAdminEmailList(booking.getWorkspaceId());
      adminList = adminUserList.stream().map(UserDTO::new).collect(Collectors.toList());
      if (adminList.size() > 0) {
        emailServiceV2.sendRequestApprovalBookingHotelEmail(booking, bookingDetailInfoResponse, adminList, "admin");
      }
    }

    if (company.getAirEmSetting().isReservAgency()) {
      List<UserDTO> managerList = new ArrayList<>();
      List<UserDTO> manager = customerService.getBtmsManagerEmailDTOList(booking.getCompanyId(), ManagerType.Hotel_RentCar, !booking.getDiFg().equals("Y"));
      if (manager.size() > 0) {
        managerList.addAll(manager);
        emailServiceV2.sendRequestApprovalBookingHotelEmail(booking, bookingDetailInfoResponse, managerList, "manager");
      }
    }
  }
}
