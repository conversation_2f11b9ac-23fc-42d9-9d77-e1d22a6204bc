package com.tixit.btms.job;

import com.tixit.btms.domain.constants.Constants;
import com.tixit.btms.inicis.InicisSettlementReqeust;
import com.tixit.btms.service.settlement.InicisSettlementService;
import com.tixit.btms.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;


/**
 * KG이니시스 승인/정산대사 데이타 전송
 */
@Slf4j
@Component("inicisSettlementJob")
// @Profile({"agency", "hn-agency", "prod-agency"})
public class InicisSettlementJob {

	@Autowired
	private InicisSettlementService inicisSettlementService;

	private final static boolean jobServer;
	static {
		jobServer = Boolean.parseBoolean(System.getProperty("job.scheduler"));
	}

	public void run() {

		//Job 직접실행 시 파라미터가 없다면 하루전날 조회
		Calendar cal = Calendar.getInstance();
		String format = "yyyyMMdd";
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		log.info("### Job 실행 today : "+ sdf.format(cal.getTime()));
		cal.add(cal.DATE, -1);

		String searchYmd = sdf.format(cal.getTime());
		log.info("### Job 실행 yesterday : "+ searchYmd);

		this.callInicisSettlement(searchYmd);
	}

	public void callInicisSettlement(String searchYmd) {

		log.info("### InicisSettlementJob START ### : "+ DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));
		log.info("### InicisSettlementJob searchYmd ### : "+ searchYmd);

		//--------------------------------------------------------------------------------------------------------------
		// 그룹아이디로 조회한다.
		// 단, 법인 ADMIN (GID : TIdesq17GI) 는 mid가 다수 존재 하고 있어 mid 로 조회 해야 한다.
		//--------------------------------------------------------------------------------------------------------------
		//	법인 GDS 	 : GID TASF732665 PW tide89@#
		//	- 732665PT9D : 세이버 MID
		//	- TZ9A000001 : 셀커넥 MID
		//	법인 ADMIN 	 : GID TIdesq17GI PW ts0102@#
		//	- tidebiz001 : 법인단체 MID
		//	- tidebiz002 : 법인항공 MID
		//	- tidebiz003 : 법인호텔 MID
		//	- tidebiz004 : 법인비자 MID
		//	- tidebiz005 : 법인예비 MID (삼성 PG)
		//
		//	상용 GDS		 : GID TASF173105 PW xndjqltm1!
		//	- 731052LD39 : 세이버 MID
		//	- TSE11131GA : 셀커넥 MID
		//	상용 ADMIN	 : GID Gtstourvis PW xndjqltm1!
		//	- tstourvis2 : 항공 MID
		//	- tstourvis4 : 단체 MID
		//	- tstourvis5 : 비자/보험 MID
		//	- tstourvis7 : 직가맹 MID
		//--------------------------------------------------------------------------------------------------------------

		if (jobServer == false) {
			log.info("### InicisSettlementJob IS NOT JOB SERVER ### "+ DateUtil.getNow());
			return;
		}

		try {
			InicisSettlementReqeust corpGds = new InicisSettlementReqeust(InicisSettlementReqeust.UrlIdType.GroupId, Constants.INICIS_GROUP_ID_IS_CORPORATION_GDS, Constants.INICIS_PASSWORD_IS_CORPORATION_GDS, searchYmd);
			log.debug("법인 GDS 요청URL = {}", corpGds.getSettlementUrl());
			String data = inicisSettlementService.callSettlementData(corpGds);

			inicisSettlementService.createSettlementData(corpGds.getUrlid(), data);
			log.debug("법인 GDS 응답결과 = {}", data);

		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}

		try {
			InicisSettlementReqeust corpAdmin = new InicisSettlementReqeust(InicisSettlementReqeust.UrlIdType.MerchantId, Constants.INICIS_MERCHANT_ID_IS_CORPORATION_GROUP, Constants.INICIS_PASSWORD_IS_CORPORATION_ADMIN, searchYmd);
			log.debug("법인 ADMIN 법인단체 요청URL = {}", corpAdmin.getSettlementUrl());

			String data = inicisSettlementService.callSettlementData(corpAdmin);
			log.debug("법인 ADMIN 법인단체 응답결과 = {}", data);

			inicisSettlementService.createSettlementData(Constants.INICIS_GROUP_ID_IS_CORPORATION_ADMIN, data);

		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}

		try {
			InicisSettlementReqeust corpAdmin = new InicisSettlementReqeust(InicisSettlementReqeust.UrlIdType.MerchantId, Constants.INICIS_MERCHANT_ID_IS_CORPORATION_AIR, Constants.INICIS_PASSWORD_IS_CORPORATION_ADMIN, searchYmd);
			log.debug("법인 ADMIN 법인항공 요청URL = {}", corpAdmin.getSettlementUrl());

			String data = inicisSettlementService.callSettlementData(corpAdmin);
			log.debug("법인 ADMIN 법인항공 응답결과 = {}", data);

			inicisSettlementService.createSettlementData(Constants.INICIS_GROUP_ID_IS_CORPORATION_ADMIN, data);

		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}

		try {
			InicisSettlementReqeust corpAdmin = new InicisSettlementReqeust(InicisSettlementReqeust.UrlIdType.MerchantId, Constants.INICIS_MERCHANT_ID_IS_CORPORATION_HOTEL, Constants.INICIS_PASSWORD_IS_CORPORATION_ADMIN, searchYmd);
			log.debug("법인 ADMIN 법인호텔 요청URL = {}", corpAdmin.getSettlementUrl());

			String data = inicisSettlementService.callSettlementData(corpAdmin);
			log.debug("법인 ADMIN 법인호텔 응답결과 = {}", data);

			inicisSettlementService.createSettlementData(Constants.INICIS_GROUP_ID_IS_CORPORATION_ADMIN, data);

		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}

		try {
			InicisSettlementReqeust corpAdmin = new InicisSettlementReqeust(InicisSettlementReqeust.UrlIdType.MerchantId, Constants.INICIS_MERCHANT_ID_IS_CORPORATION_VISA, Constants.INICIS_PASSWORD_IS_CORPORATION_ADMIN, searchYmd);
			log.debug("법인 ADMIN 법인비자 요청URL = {}", corpAdmin.getSettlementUrl());

			String data = inicisSettlementService.callSettlementData(corpAdmin);
			log.debug("법인 ADMIN 법인비자 응답결과 = {}", data);

			inicisSettlementService.createSettlementData(Constants.INICIS_GROUP_ID_IS_CORPORATION_ADMIN, data);

		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}

		try {
			InicisSettlementReqeust corpAdmin = new InicisSettlementReqeust(InicisSettlementReqeust.UrlIdType.MerchantId, Constants.INICIS_MERCHANT_ID_IS_CORPORATION_SAMSUNG, Constants.INICIS_PASSWORD_IS_CORPORATION_ADMIN, searchYmd);
			log.debug("법인 ADMIN 법인삼성 요청URL = {}", corpAdmin.getSettlementUrl());

			String data = inicisSettlementService.callSettlementData(corpAdmin);
			log.debug("법인 ADMIN 법인삼성 응답결과 = {}", data);

			inicisSettlementService.createSettlementData(Constants.INICIS_GROUP_ID_IS_CORPORATION_ADMIN, data);

		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}

		try {
			InicisSettlementReqeust commGds = new InicisSettlementReqeust(InicisSettlementReqeust.UrlIdType.GroupId, Constants.INICIS_GROUP_ID_IS_COMMERCIAL_GDS, Constants.INICIS_PASSWORD_IS_COMMERCIAL_GDS, searchYmd);
			log.debug("상용 GDS 요청URL = {}", commGds.getSettlementUrl());

			String data = inicisSettlementService.callSettlementData(commGds);
			log.debug("상용 GDS 응답결과 = {}", data);

			inicisSettlementService.createSettlementData(commGds.getUrlid(), data);

		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}

		try {
			InicisSettlementReqeust commAdmin = new InicisSettlementReqeust(InicisSettlementReqeust.UrlIdType.GroupId, Constants.INICIS_GROUP_ID_IS_COMMERCIAL_ADMIN, Constants.INICIS_PASSWORD_IS_COMMERCIAL_ADMIN, searchYmd);
			log.debug("상용 ADMIN 요청URL = {}", commAdmin.getSettlementUrl());

			String data = inicisSettlementService.callSettlementData(commAdmin);
			log.debug("상용 ADMIN 응답결과 = {}", data);

			inicisSettlementService.createSettlementData(commAdmin.getUrlid(), data);

		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}

		log.info("### InicisSettlementJob END ### : "+ DateUtil.getNow());
	}
}
