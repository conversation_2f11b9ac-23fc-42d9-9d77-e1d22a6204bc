package com.tixit.btms.job;

import com.tixit.btms.domain.common.Airline;
import com.tixit.btms.domain.common.Airport;
import com.tixit.btms.domain.travel.BookingAir;
import com.tixit.btms.domain.travel.BookingAirSchedule;
import com.tixit.btms.domain.travel.BookingAirTraveler;
import com.tixit.btms.domain.travel.PnrDataHistory;
import com.tixit.btms.domain.user.Customer;
import com.tixit.btms.domain.user.CustomerPassport;
import com.tixit.btms.domain.user.User;
import com.tixit.btms.service.travel.BookingAirService;
import com.tixit.btms.service.user.CustomerService;
import com.tixit.btms.util.CommonUtil;
import com.tixit.btms.util.DateUtil;
import com.tixit.btms.util.PnrParserUtil;
import com.tixit.btms.util.SFTPUtil;
import lombok.extern.slf4j.Slf4j;
import module.air.enumeration.GDSType;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component("isosJob")
// @Profile({"agency", "hn-agency", "prod-agency"})
public class ISOSJob {

	@Value("${btms.env.type}")
    private String btmsEnvType;

	@Autowired
	private BookingAirService bookingAirService;

	@Autowired
	private CustomerService customerService;

	private String searchTicketYmd = null;

	private final static boolean jobServer;
	static {
		jobServer = Boolean.parseBoolean(System.getProperty("job.scheduler"));
	}

	private String REG_AMADEUS_PHONE = "APM\\s*[SEL]*\\s*(0[0-9])\\-([0-9]{3,4})\\-([0-9]{4})[\\/P1]*";
	private String REG_AMADEUS_PHONE2 = "APM\\s*[SEL]*\\s*([0-9]{2})\\-([0-9]{2})\\-([0-9]{3,4})\\-([0-9]{4,5})[\\/P1]*";
	private String REG_SABRE_PHONE = "SELM\\*(0[0-9])\\-([0-9]{3,4})\\-([0-9]{4})";

	public void setSearchTicketYmd(String searchYmd) {
		this.searchTicketYmd = searchYmd;
	}

	/**
	 * 매일 새벽 2시 마다 Scheduler 실행.
	 */
	public void run() {
		if (jobServer == false) {
			return;
		} 
		setSearchTicketYmd(null);
		log.info("### ISOS Job 시작 ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));

//		추출 거래처
//		B72 : 현대카드 주식회사		- HYUNDAICARD
//		B73 : 현대캐피탈			- HYUNDAICARD
//		B74 : 현대커머셜주식회사		- HYUNDAICARD
//		D19 : 레이텀앤왓킨스		- LATHAMWATKINS
//		D01, D02, D84 : 오므론그룹	- OMRONKOREA
//		S02 : SK ENS			- SKENS

		//Job 직접실행 시 파라미터가 없다면 하루전날 조회
		boolean isDefalutXmlFileName = false;
		if (CommonUtil.isNullOrSpace(searchTicketYmd)) {
			setSearchTicketYmd(DateUtil.getYmdByPlusDays(-1));
			isDefalutXmlFileName = true;
		}
		List<String> uploadFileNameList = new ArrayList<>();
		List<String> siteCodeList = new ArrayList<>();
		siteCodeList.add("B72");
		siteCodeList.add("B73");
		siteCodeList.add("B74");

		List<Map<String, Object>> isosList = bookingAirService.getBookingAirList4ISOS(searchTicketYmd, siteCodeList);

		//1.현대카드 주식회사, 현대캐피탈, 현대커머셜주식회사 - pHYUNDAICARD_yyyyMMddHHmmss.xml
		String fileFullPath = this.makeXmlFile(isosList, "HYUNDAICARD", isDefalutXmlFileName);
		uploadFileNameList.add(fileFullPath);

		siteCodeList = new ArrayList<>();
		siteCodeList.add("D19");

		isosList = bookingAirService.getBookingAirList4ISOS(searchTicketYmd, siteCodeList);

		//2.레이텀앤왓킨스 - pLATHAMWATKINS_yyyyMMddHHmmss.xml
		fileFullPath = this.makeXmlFile(isosList, "LATHAMWATKINS", isDefalutXmlFileName);
		uploadFileNameList.add(fileFullPath);

		siteCodeList = new ArrayList<>();
		siteCodeList.add("D01");
		siteCodeList.add("D02");
		siteCodeList.add("D84");

		isosList = bookingAirService.getBookingAirList4ISOS(searchTicketYmd, siteCodeList);

		//3.오므론그룹 - pOMRONKOREA_yyyyMMddHHmmss.xml
		fileFullPath = this.makeXmlFile(isosList, "OMRONKOREA", isDefalutXmlFileName);
		uploadFileNameList.add(fileFullPath);

		siteCodeList = new ArrayList<>();
		siteCodeList.add("S02");

		isosList = bookingAirService.getBookingAirList4ISOS(searchTicketYmd, siteCodeList);

		//4.SK ENS - tSKENS_yyyyMMddHHmmss.xml
		fileFullPath = this.makeXmlFile(isosList, "SKENS", isDefalutXmlFileName);
		uploadFileNameList.add(fileFullPath);

		setSearchTicketYmd(null);

		if (btmsEnvType.equals("prod")) {
			// only prod env will upload file
			SFTPUtil sftpUtil = null;
			try {
				sftpUtil = new SFTPUtil("44.194.9.9", 22, "Hyundai_TideSquare", "4626qSKy");
				sftpUtil.connect();

				for (String uploadFileFullPath : uploadFileNameList) {
					File xmlFile = new File(uploadFileFullPath);

					if (xmlFile != null) {
						sftpUtil.upload("", xmlFile, true);
					}
				}
				sftpUtil.disconnect();
				log.info("\n");
				log.info("### ISOS SFTP Upload 완료 ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));
			} catch (Exception e) {
				log.info("\n");
				log.info("### ISOS SFTP Upload 실패 ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));
				log.error(ExceptionUtils.getStackTrace(e));
				e.printStackTrace();
			} finally {
				if (sftpUtil != null) {
					sftpUtil.disconnect();
				}
			}
		}
		log.info("\n");
		log.info("### ISOS Job 완료 ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));
	}

	private String makeXmlFile(List<Map<String, Object>> isosList, String partFileName, boolean isDefalutXmlFileName) {

		String uploadPath = "/app/upload/isos/";
		String uploadPath2 = "/app/servers/BTMS-agency/isos/";
		String today = null;

		if (isDefalutXmlFileName) {
			today = DateUtil.getCurrentDate("yyyyMMddHHmmss");
		} else {
			today = this.searchTicketYmd;
		}
		String saveFileName = "p"+ partFileName +"_"+ today +".xml";

		if ("SKENS".equals(partFileName)) {
			saveFileName = "t"+ partFileName +"_"+ today +".xml";
		}
		String fileFullPath = uploadPath + saveFileName;
		String fileFullPath2 = uploadPath2 + saveFileName;

		try {
			DocumentBuilderFactory docFactory = DocumentBuilderFactory.newInstance();
			DocumentBuilder docBuilder = docFactory.newDocumentBuilder();

			// XML 파일로 쓰기
			TransformerFactory transformerFactory = TransformerFactory.newInstance();
			Transformer transformer = transformerFactory.newTransformer();

			transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
			transformer.setOutputProperty(OutputKeys.INDENT, "yes");
			transformer.setOutputProperty(OutputKeys.STANDALONE, "yes");

			Document doc = docBuilder.newDocument();
			Element rootElement = doc.createElement("PNRList");
			doc.appendChild(rootElement);

			if (isosList != null && isosList.size() > 0) {

				for (Map<String, Object> isos : isosList) {

					String siteCode = (String) isos.get("SITECODE");
					String bookingAirIds = (String) isos.get("BOOKINGAIRIDS");
					String[] arrayBookingAirId = bookingAirIds.split(",", -1);

					for (String bookingAirId : arrayBookingAirId) {

						if (!CommonUtil.isNullOrSpace(bookingAirId)) {

							//1.PNR 엘리먼트
							Element pnr = doc.createElement("PNR");
							rootElement.appendChild(pnr);

							BookingAir bookingAir = bookingAirService.getOne(Long.parseLong(bookingAirId));

							Element recordLocator = doc.createElement("RecordLocator");
							if (GDSType.AMADEUS.equals(bookingAir.getGdsType())) {
								recordLocator.appendChild(doc.createTextNode(bookingAir.getOtherPnrNo()));
							} else {
								recordLocator.appendChild(doc.createTextNode(bookingAir.getPnrNo()));
							}
							pnr.appendChild(recordLocator);

							Element clientIdentifier = doc.createElement("ClientIdentifier");
							clientIdentifier.appendChild(doc.createTextNode(partFileName));
							pnr.appendChild(clientIdentifier);

							//탑승객 List
							Element travelerList = doc.createElement("TravelerList");
							List<BookingAirTraveler> bookingAirTravelerList = bookingAir.getBookingAirTravelers();
							//EMAIL, CELLPHONE Parsing Map
							Map<String, String> emailPhoneMap = this.parsingPnrData(bookingAir.getGdsType(), bookingAir.getPnrDataHistories());
							int travelerCount = 0;

							for (BookingAirTraveler bookingAirTraveler : bookingAirTravelerList) {

								Element traveler = doc.createElement("Traveler");

								String firstNameStr = CommonUtil.isNullOrSpace(bookingAirTraveler.getFirstName()) ? " " : bookingAirTraveler.getFirstName().replaceAll(" ", "");
								String lastNameStr = CommonUtil.isNullOrSpace(bookingAirTraveler.getLastName()) ? " " : bookingAirTraveler.getLastName().replaceAll(" ", "");
								String emailAddressStr = CommonUtil.isNullOrSpace(emailPhoneMap.get("EMAIL_"+travelerCount)) ? " " : emailPhoneMap.get("EMAIL_"+travelerCount).replaceAll(" ", "");
								String phoneNumberStr = CommonUtil.isNullOrSpace(emailPhoneMap.get("PHONE_"+travelerCount)) ? " " : emailPhoneMap.get("PHONE_"+travelerCount).replaceAll(" ", "");
								String employeeNo = " ";

								//레이텀앤왓킨스(D19) 만 회원매칭 정보로 Set, CustomData 정보
								if ("D19".equals(siteCode)) {

									User travelerUser = bookingAirTraveler.getTraveler();
									if (null != travelerUser) {

										Customer customer = customerService.getCustomer(travelerUser.getId());
										CustomerPassport customerPassport = customer.getCustomerPassport();

										if (null != customerPassport) {
											firstNameStr = customerPassport.getFirstName();
											lastNameStr = customerPassport.getLastName();
										}
										emailAddressStr = travelerUser.getEmail();
										phoneNumberStr = travelerUser.getCellPhoneNumber();
										if (!CommonUtil.isNullOrSpace(phoneNumberStr)) {
											phoneNumberStr = "+82" + phoneNumberStr.substring(1);
											phoneNumberStr = CommonUtil.isNullOrSpace(phoneNumberStr) ? " " : phoneNumberStr.replaceAll(" ", "");
										}
										employeeNo = bookingAirTraveler.getTraveler().getEmployeeNo();

										firstNameStr = CommonUtil.isNullOrSpace(firstNameStr) ? " " : firstNameStr.replaceAll(" ", "");
										lastNameStr = CommonUtil.isNullOrSpace(lastNameStr) ? " " : lastNameStr.replaceAll(" ", "");
										emailAddressStr = CommonUtil.isNullOrSpace(emailAddressStr) ? " " : emailAddressStr.replaceAll(" ", "");
										employeeNo = CommonUtil.isNullOrSpace(employeeNo) ? " " : employeeNo.replaceAll(" ", "");
									}
								}

								Element firstName = doc.createElement("FirstName");
								firstName.appendChild(doc.createTextNode( firstNameStr ));
								traveler.appendChild(firstName);

								Element lastName = doc.createElement("LastName");
								lastName.appendChild(doc.createTextNode( lastNameStr ));
								traveler.appendChild(lastName);

								Element emailAddress = doc.createElement("EmailAddress");
								emailAddress.appendChild(doc.createTextNode( emailAddressStr ));
								traveler.appendChild(emailAddress);

								Element phoneNumber = doc.createElement("PhoneNumber");
								phoneNumber.appendChild(doc.createTextNode( phoneNumberStr ));

								traveler.appendChild(phoneNumber);

								//레이텀앤왓킨스(D19) 만 CustomData 회원매칭 정보 Set
								if ("D19".equals(siteCode)) {

									Element customData = doc.createElement("CustomData");

									Element customField = doc.createElement("CustomField");

									Element name = doc.createElement("Name");
									name.appendChild(doc.createTextNode("CUSTOMER NUMBER"));
									customField.appendChild(name);

									Element value = doc.createElement("Value");
									value.appendChild(doc.createTextNode("LATH001"));
									customField.appendChild(value);

									customData.appendChild(customField);

									customField = doc.createElement("CustomField");

									name = doc.createElement("Name");
									name.appendChild(doc.createTextNode("EMPLOYEE ID"));
									customField.appendChild(name);

									value = doc.createElement("Value");
									value.appendChild(doc.createTextNode( employeeNo ));
									customField.appendChild(value);

									customData.appendChild(customField);

									traveler.appendChild(customData);
								}
								travelerList.appendChild(traveler);
								travelerCount++;
							}
							pnr.appendChild(travelerList);

							//여정 List
							Element segmentList = doc.createElement("SegmentList");
							List<BookingAirSchedule> bookingAirScheduleList = bookingAir.getBookingAirSchedules();

							for (BookingAirSchedule bookingAirSchedule : bookingAirScheduleList) {

								Element airSegment = doc.createElement("AirSegment");

								Element airlineCode = doc.createElement("AirlineCode");
								airlineCode.appendChild(doc.createTextNode( Optional.of(bookingAirSchedule.getAirline()).map(Airline::getCode).orElse(" ") ));
								airSegment.appendChild(airlineCode);

								Element flightNumber = doc.createElement("FlightNumber");
								flightNumber.appendChild(doc.createTextNode( CommonUtil.isNullOrSpace(bookingAirSchedule.getAirlineFlightNo()) ? " " : bookingAirSchedule.getAirlineFlightNo() ));
								airSegment.appendChild(flightNumber);

								Element departureIATACode = doc.createElement("DepartureIATACode");
								departureIATACode.appendChild(doc.createTextNode( Optional.of(bookingAirSchedule.getFromAirport()).map(Airport::getCode).orElse(" ") ));
								airSegment.appendChild(departureIATACode);

								Element departureTime = doc.createElement("DepartureTime");
								departureTime.appendChild(doc.createTextNode( Optional.of(bookingAirSchedule.getFromDate()).map(fromDate -> DateUtil.date2String(bookingAirSchedule.getFromDate(), "yyyy-MM-dd'T'HH:mm:ss")).orElse(" ") ));
								airSegment.appendChild(departureTime);

								Element arrivalIATACode = doc.createElement("ArrivalIATACode");
								arrivalIATACode.appendChild(doc.createTextNode( Optional.of(bookingAirSchedule.getToAirport()).map(Airport::getCode).orElse(" ") ));
								airSegment.appendChild(arrivalIATACode);

								Element arrivalTime = doc.createElement("ArrivalTime");
								arrivalTime.appendChild(doc.createTextNode( Optional.of(bookingAirSchedule.getToDate()).map(toDate -> DateUtil.date2String(bookingAirSchedule.getToDate(), "yyyy-MM-dd'T'HH:mm:ss")).orElse(" ") ));
								airSegment.appendChild(arrivalTime);

								segmentList.appendChild(airSegment);
							}
							pnr.appendChild(segmentList);

							//CustomData - 현대카드 주식회사	(B72), 현대캐피탈(B73), 현대커머셜주식회사(B74) 만 내용 생성
							Element customData = doc.createElement("CustomData");

							if ("B72".equals(siteCode) || "B73".equals(siteCode) || "B74".equals(siteCode)) {

								Element customField = doc.createElement("CustomField");

								Element name = doc.createElement("Name");
								name.appendChild(doc.createTextNode("Business Unit"));
								customField.appendChild(name);

								Element value = doc.createElement("Value");

								if ("B72".equals(siteCode)) {
									value.appendChild(doc.createTextNode("HYUNDAI CARD"));
								} else if ("B73".equals(siteCode)) {
									value.appendChild(doc.createTextNode("HYUNDAI CAPITAL"));
								} else if ("B74".equals(siteCode)) {
									value.appendChild(doc.createTextNode("HYUNDAI COMMERCIAL"));
								}
								customField.appendChild(value);

								customData.appendChild(customField);
							}
							pnr.appendChild(customData);
						}
					}
				}
			} else {
				log.info("### ISOS Job No ISOS List ###");
			}
			DOMSource source = new DOMSource(doc);
			StreamResult result = new StreamResult(new FileOutputStream(new File(fileFullPath)));
			StreamResult result2 = new StreamResult(new FileOutputStream(new File(fileFullPath2)));

			transformer.transform(source, result);
			transformer.transform(source, result2);
			log.info("### ISOS Job XML File Creation ### : "+ fileFullPath);
			log.info("### ISOS Job XML File Creation ### : "+ fileFullPath2);

		} catch (Exception e) {
			log.error("### ISOS Job Error ###"+ e.getMessage());
			log.error(ExceptionUtils.getStackTrace(e));
		}
		return fileFullPath2;
	}

	private Map<String, String> parsingPnrData(GDSType gdsType, List<PnrDataHistory> pnrDataHistoryList) {

		Map<String, String> emailPhoneResultMap = new HashMap<>();
		String lastPnrData = pnrDataHistoryList.stream()
			.sorted(Comparator.comparing(PnrDataHistory::getCreateDate).reversed())
			.findFirst().get().getPnrData();

		if (lastPnrData != null) {

			Pattern emailPattern = null;
			Pattern mobilePattern = null;
			Pattern phonePattern = null;
			Pattern phonePattern2 = null;

			if (GDSType.AMADEUS.equals(gdsType)) {
				emailPattern = Pattern.compile(PnrParserUtil.REG_AMADEUS_EMAIL);
				mobilePattern = Pattern.compile(PnrParserUtil.REG_AMADEUS_MOBILE);
				phonePattern = Pattern.compile(this.REG_AMADEUS_PHONE);
				phonePattern2 = Pattern.compile(this.REG_AMADEUS_PHONE2);

			} else {
				lastPnrData = lastPnrData.replaceAll("？¤", "@");
				emailPattern = Pattern.compile(PnrParserUtil.REG_SABRE_EMAIL);
				mobilePattern = Pattern.compile(PnrParserUtil.REG_SABRE_MOBILE);
				phonePattern = Pattern.compile(this.REG_SABRE_PHONE);
			}
			Matcher matcher = emailPattern.matcher(lastPnrData);
			int matcherCount = 0;
			while (matcher.find()) {
				emailPhoneResultMap.put("EMAIL_"+ matcherCount, matcher.group(1).replaceAll("//", "@"));
				matcherCount++;
			}

			matcher = mobilePattern.matcher(lastPnrData);
			matcherCount = 0;
			while (matcher.find()) {
				String cellPhoneNumber = matcher.group(1) + matcher.group(2) + matcher.group(3);

				if (!CommonUtil.isNullOrSpace(cellPhoneNumber)) {
					cellPhoneNumber = "+82" + cellPhoneNumber.substring(1);
				}
				emailPhoneResultMap.put("PHONE_"+ matcherCount, cellPhoneNumber);
				matcherCount++;
			}
			//핸드폰번호가 아닌 일반전화일 때..
			if (matcherCount == 0) {
				matcher = phonePattern.matcher(lastPnrData);

				while (matcher.find()) {
					String phoneNumber = matcher.group(1) + matcher.group(2) + matcher.group(3);
					emailPhoneResultMap.put("PHONE_"+ matcherCount, phoneNumber);
					matcherCount++;
				}
			}
			//일반전화 두번째 패턴
			if (matcherCount == 0 && phonePattern2 != null) {
				matcher = phonePattern2.matcher(lastPnrData);
				
				while (matcher.find()) {
					String phoneNumber = matcher.group(1) + matcher.group(2) + matcher.group(3);
					emailPhoneResultMap.put("PHONE_"+ matcherCount, phoneNumber);
					matcherCount++;
				}
			}
		}
		return emailPhoneResultMap;
	}
}
