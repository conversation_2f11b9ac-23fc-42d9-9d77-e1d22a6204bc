package com.tixit.btms.job;

import com.tixit.btms.domain.common.BatchJobs;
import com.tixit.btms.domain.constants.Constants;
import com.tixit.btms.dto.travel.HyundaiCardTicketDTO;
import com.tixit.btms.dto.travel.HyundaiCardTicketSearchDTO;
import com.tixit.btms.service.common.BatchJobsService;
import com.tixit.btms.service.travel.BookingAirTicketService;
import com.tixit.btms.util.CommonUtil;
import com.tixit.btms.util.DateUtil;
import com.tixit.btms.util.SFTPUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Component("hyundaiCardTicketJob")
// @Profile({"agency", "hn-agency", "prod-agency"})
public class HyundaiCardTicketJob {

	@Value("${btms.env.type}")
    private String btmsEnvType;

	@Autowired
	private BookingAirTicketService bookingAirTicketService;
	
	@Autowired
	private BatchJobsService batchJobsService;

	@Value("${hyundai.card.ftp.ip}")
	private String ftpIp;

	@Value("${hyundai.card.ftp.user}")
	private String ftpUser;

	@Value("${hyundai.card.ftp.password}")
	private String ftpPassword;
	
	private static final String batchJobCode = "HYUNDAICARD";

	private String searchTicketYmd = null;

	private final static boolean jobServer;
	static {
		jobServer = Boolean.parseBoolean(System.getProperty("job.scheduler"));
	}

	public void setSearchTicketYmd(String searchYmd) {
		this.searchTicketYmd = searchYmd;
	}

	/**
	 * 매일 오전 6시 마다 Scheduler 실행.
	 */
	public void run() {

		if (jobServer == false) {
			return;
		}
		
		BatchJobs batchJob = batchJobsService.getBatchJobByCode(batchJobCode);
		
		if ( batchJob != null && !batchJob.getIsEnabled() ) {
			log.info("### Hyundai Ticket Job Stopped ### ");
			return;
		}

		log.info("### Hyundai Ticket Job Start ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));

		List<String> fileFullPathList = new ArrayList<>();

		log.info("### Hyundai Ticket File Create Start ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));

		if (null != getArraySearchYmd() && getArraySearchYmd().length > 0) {
			String hr01FileFullPath = this.makeFile(this.getHR01(getArraySearchYmd()), "HR01_btms");

			if (!CommonUtil.isNullOrSpace(hr01FileFullPath)) {
				fileFullPathList.add(hr01FileFullPath);
			}

			String hr02FileFullPath = this.makeFile(this.getHR02(getArraySearchYmd()), "HR02_btms");

			if (!CommonUtil.isNullOrSpace(hr02FileFullPath)) {
				fileFullPathList.add(hr02FileFullPath);
			}

			String hr04FileFullPath = this.makeFile(this.getHR04(getArraySearchYmd()), "HR04_btms");

			if (!CommonUtil.isNullOrSpace(hr04FileFullPath)) {
				fileFullPathList.add(hr04FileFullPath);
			}
		}

		log.info("### Hyundai Ticket File Create End ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));

		if (btmsEnvType.equals("prod")) {
			// only prod env will upload file
			log.info("### Hyundai Ticket SFTP File Upload Start ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));

			if (fileFullPathList.size() > 0) {
				this.sftpUpload(fileFullPathList);
			}

			log.info("### Hyundai Ticket SFTP FileUpload End ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));
		}

		this.searchTicketYmd = null;

		log.info("### Hyundai Ticket Job End ### : " + DateUtil.getFormatString("yyyy-MM-dd HH:mm:ss"));
	}

	public String getHR01(String[] arrayYmd) {

		HyundaiCardTicketSearchDTO searchDTO = new HyundaiCardTicketSearchDTO();
		searchDTO.setArrayYmd(arrayYmd);

		List<HyundaiCardTicketDTO> list = bookingAirTicketService.getList4HyundaiCardHR01(searchDTO);
		StringBuilder sb = new StringBuilder();

		for (HyundaiCardTicketDTO hyundaiCardTicketDTO : list) {
			try {
				if( Integer.parseInt(hyundaiCardTicketDTO.getFareAmt()) +
					Integer.parseInt(hyundaiCardTicketDTO.getDscnAmt()) +
					Integer.parseInt(hyundaiCardTicketDTO.getCashStlmAmt()) +
					Integer.parseInt(hyundaiCardTicketDTO.getCardStlmAmt()) == 0) {
					continue;
				}
				
				sb.append("BD");	// 레코드구분
				sb.append(hyundaiCardTicketDTO.getArcoCd());	// 항공사코드
				sb.append(hyundaiCardTicketDTO.getTcktNo());	// 티켓번호
				sb.append(CommonUtil.spacefill(!CommonUtil.isNullOrSpace(hyundaiCardTicketDTO.getCnntTcktNo())
											   && hyundaiCardTicketDTO.getCnntTcktNo().length() > 3 ? hyundaiCardTicketDTO.getCnntTcktNo().substring(0, 3)
						: hyundaiCardTicketDTO.getCnntTcktNo(), 3));	// 연결티켓번호
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getPrtsTcktNo(), 10));	// 부모티켓번호
				sb.append(hyundaiCardTicketDTO.getTcktStatClsfCd());	// 티켓상태구분코드
				String bdspEngName = hyundaiCardTicketDTO.getBdpsEngNm();
				if( bdspEngName != null && bdspEngName.length() > 32 ) {
					bdspEngName = bdspEngName.substring(0, 32);
				}
				sb.append(CommonUtil.spacefill(bdspEngName, 32));	// 탑승자영문명
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getFareAmt(), 13));		// 운임금액
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getDscnAmt(), 13));		// 할인금액
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getCashStlmAmt(), 13));	// 현금결제금액
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getCardStlmAmt(), 13));	// 카드결제금액
				sb.append("DC");	// 결제카드회사코드
				//			sb.append(CommonUtil.spacefill(this.getCardNumber(hyundaiCardTicketDTO.getCompanyId()), 32));	// 카드번호
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getCdno(), 32));	// 카드번호
				sb.append(hyundaiCardTicketDTO.getCmsnRtoInfo());	// 커미션비율정보
				sb.append(CommonUtil.spacefill("0", 13));		// 커미션금액
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getTax(), 13));		// 세금
				sb.append(CommonUtil.spacefill(String.valueOf(Integer.parseInt(hyundaiCardTicketDTO.getFareAmt()) +
															  Integer.parseInt(hyundaiCardTicketDTO.getTax())), 13));		// 판매금액
				sb.append(hyundaiCardTicketDTO.getIsbnBrnCd());		// 발권지점코드
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getArvlCityNm(), 3));	// 도착도시명
				sb.append(CommonUtil.spacefill("", 1));		// 성인구분코드
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getTrruItm().replaceAll(",", ""), 45));		// 여정내역
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getDptrDt(), 8));	// 출발일자
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getRwrdAfbn(), 10));	// 리워드제휴점번호
				sb.append(CommonUtil.spacefill("", 8));		// 영업사원코드
				sb.append(CommonUtil.spacefill(!CommonUtil.isNullOrSpace(hyundaiCardTicketDTO.getIsbnEmpCd())
											   && hyundaiCardTicketDTO.getIsbnEmpCd().length() > 8 ? hyundaiCardTicketDTO.getIsbnEmpCd().substring(0, 8)
						: hyundaiCardTicketDTO.getIsbnEmpCd(), 8));		// 발권사원코드
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getIsbnDt(), 8));	// 발권일자
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getNatnZoneClsfCd(), 2));	// 국가지역구분코드
				sb.append(hyundaiCardTicketDTO.getWaplIcmgPathClsfCd());	// 신청서유입경로구분코드
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getOrglTcktNo(), 10));		// 원티켓번호
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getAvNo(), 8));		// 승인번호
				sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getAvDt(), 8) + "@");		// 승인일자
			} catch (NullPointerException e) {
				log.error("NullPointerException : " + hyundaiCardTicketDTO.toString(),e);
				continue;
			}

		}

		log.debug("--------------------------------------------------------");
		log.debug("HR01 Contents");
		log.debug(sb.toString());
		log.debug("--------------------------------------------------------");

		return sb.toString();
	}

	public String getHR02(String[] arrayYmd) {
		
		HyundaiCardTicketSearchDTO searchDTO = new HyundaiCardTicketSearchDTO();
		searchDTO.setArrayYmd(arrayYmd);

		List<HyundaiCardTicketDTO> list = bookingAirTicketService.getList4HyundaiCardHR02(searchDTO);
		StringBuilder sb = new StringBuilder();

		for (HyundaiCardTicketDTO hyundaiCardTicketDTO : list) {
			if( Integer.parseInt(hyundaiCardTicketDTO.getFareAmt()) +
				Integer.parseInt(hyundaiCardTicketDTO.getTax()) +
				Integer.parseInt(hyundaiCardTicketDTO.getCashStlmAmt()) +
				Integer.parseInt(hyundaiCardTicketDTO.getCardStlmAmt()) == 0) {
				continue;
			}
			sb.append("BD");	// 레코드구분
			sb.append(hyundaiCardTicketDTO.getTcktNo());	// 티켓번호
			sb.append(hyundaiCardTicketDTO.getTcktStatClsfCd());	// 티켓상태구분코드
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getDomesticBdpsEngNm().length() > 20
					? hyundaiCardTicketDTO.getDomesticBdpsEngNm().substring(0, 20) : hyundaiCardTicketDTO.getDomesticBdpsEngNm(), 20));		// 탑승자영문명

			String trruItm = hyundaiCardTicketDTO.getTrruItm().replaceAll(",", "");

			sb.append(CommonUtil.spacefill(trruItm.length() > 8 ? trruItm.substring(0, 8) : trruItm, 8));	// 여정내역
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getFareAmt(), 15));		// 운임금액
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getTax(), 15));		// 세금
			sb.append(CommonUtil.spacefill("0", 15));		// 유류할증금액
			sb.append(CommonUtil.spacefill("0", 15));		// 취소수수료
			sb.append(CommonUtil.spacefill("0", 15));		// 환불수수료
			sb.append("C");		// 카드현금구분
			sb.append("VI");	// 결재카드회사값
//			sb.append(CommonUtil.spacefill(this.getCardNumber(hyundaiCardTicketDTO.getCompanyId()), 32));	// 카드번호
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getCdno(), 32));	// 카드번호
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getIsbnDt(), 8));	// 발권일자
			sb.append("KE");	// 발권유입경로값
			sb.append(hyundaiCardTicketDTO.getArcoCd());	// 항공사코드
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getAvNo(), 8));		// 승인번호
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getAvDt(), 8) + "@");		// 승인일자
		}

		log.debug("--------------------------------------------------------");
		log.debug("HR02 Contents");
		log.debug(sb.toString());
		log.debug("--------------------------------------------------------");

		return sb.toString();
	}

	public String getHR04(String[] arrayYmd) {

		HyundaiCardTicketSearchDTO searchDTO = new HyundaiCardTicketSearchDTO();
		searchDTO.setArrayYmd(arrayYmd);

		List<HyundaiCardTicketDTO> list = bookingAirTicketService.getList4HyundaiCardHR04(searchDTO);
		StringBuilder sb = new StringBuilder();

		for (HyundaiCardTicketDTO hyundaiCardTicketDTO : list) {
			if( Integer.parseInt(hyundaiCardTicketDTO.getFareAmt()) +
				Integer.parseInt(hyundaiCardTicketDTO.getTax()) +
				Integer.parseInt(hyundaiCardTicketDTO.getCashStlmAmt()) +
				Integer.parseInt(hyundaiCardTicketDTO.getCardStlmAmt()) == 0) {
				continue;
			}
			
			sb.append("BD");	// 레코드구분
			sb.append(hyundaiCardTicketDTO.getTcktNo());	// 티켓번호
			sb.append(hyundaiCardTicketDTO.getTcktStatClsfCd());	// 티켓상태구분코드
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getDomesticBdpsEngNm().length() > 20
					? hyundaiCardTicketDTO.getDomesticBdpsEngNm().substring(0, 20) : hyundaiCardTicketDTO.getDomesticBdpsEngNm(), 20));		// 탑승자영문명

			String trruItm = hyundaiCardTicketDTO.getTrruItm().replaceAll(",", "");

			sb.append(CommonUtil.spacefill(trruItm.length() > 8 ? trruItm.substring(0, 8) : trruItm, 8));	// 여정내역
			sb.append(CommonUtil.spacefill(String.valueOf(Integer.parseInt(hyundaiCardTicketDTO.getFareAmt()) +
					Integer.parseInt(hyundaiCardTicketDTO.getTax())), 15));		// 판매금액
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getFareAmt(), 15));		// 순금액
			sb.append(CommonUtil.spacefill(String.valueOf(Integer.parseInt(hyundaiCardTicketDTO.getTax()) -
					Integer.parseInt(hyundaiCardTicketDTO.getOilExcgAmt())), 15));		// 세금
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getOilExcgAmt(), 15));	// 유류할증금액
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getCardStlmAmt(), 15));	// 카드금액
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getCashStlmAmt(), 15));	// 현금금액
			sb.append("VI");	// 결재카드회사값
			sb.append(CommonUtil.spacefill(this.getCardNumber(hyundaiCardTicketDTO.getCompanyId()), 32));	// 카드번호
			sb.append("OZ");	// 발권유입경로값
			sb.append(hyundaiCardTicketDTO.getArcoCd());	// 항공사코드
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getIsbnDt(), 8));	// 발권일자
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getAvNo(), 8));		// 승인번호
			sb.append(CommonUtil.spacefill(hyundaiCardTicketDTO.getAvDt(), 8) + "@");		// 승인일자
		}

		log.debug("--------------------------------------------------------");
		log.debug("HR04 Contents");
		log.debug(sb.toString());
		log.debug("--------------------------------------------------------");

		return sb.toString();
	}

	public String getCardNumber(Long companyId) {

		if (Constants.COMPANY_IS_HYUNDAI_CARD.equals(companyId)) {
			return Constants.CARD_INFO_HYUNDAI_CARD;

		} else if (Constants.COMPANY_IS_HYUNDAI_CAPITAL.equals(companyId)) {
			return Constants.CARD_INFO_HYUNDAI_CAPITAL;

		} else if (Constants.COMPANY_IS_HYUNDAI_COMMERCIAL.equals(companyId)) {
			return Constants.CARD_INFO_HYUNDAI_COMMERCIAL;
		}
		return "";
	}

	public String[] getArraySearchYmd() {

		String[] arrayYmd = {};

		try {
			Calendar cal = Calendar.getInstance();
			String searchYmd = DateUtil.getCurrentDate();

			if (!CommonUtil.isNullOrSpace(this.searchTicketYmd)) {
				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
				Date date = dateFormat.parse(this.searchTicketYmd);

				cal.setTime(date);

				searchYmd = this.searchTicketYmd;
			}

			if (!CommonUtil.isNullOrSpace(this.searchTicketYmd)) {
				log.debug("Direct Excute Batch ... this.searchTicketYmd is " + this.searchTicketYmd);
			}

			int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);

			log.debug("DAY_OF_WEEK is " + dayOfWeek);

			if (dayOfWeek == 1 || dayOfWeek == 7) {
				return arrayYmd;

			} else if (dayOfWeek == 2) {
				arrayYmd = DateUtil.getDates(DateUtil.minusDays(searchYmd, 3), DateUtil.minusDays(searchYmd, 1));

			} else if (dayOfWeek > 2 && dayOfWeek < 7) {
				arrayYmd = DateUtil.getDates(DateUtil.minusDays(searchYmd, 1), DateUtil.minusDays(searchYmd, 1));
			}

			for (String ymd : arrayYmd) {
				log.debug("Batch Ymd : " + ymd);
			}

			return arrayYmd;

		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;
	}

	public String makeFile(String text, String fileName) {

		/*if (CommonUtil.isNullOrSpace(text.trim())) {
			return "";
		}*/

		String fileFullPath;
		BufferedWriter out = null;

		try {
			String saveDirectory = "/app/upload/hyundaicard/".concat(!CommonUtil.isNullOrSpace(this.searchTicketYmd)
					? this.searchTicketYmd : DateUtil.getCurrentDate("yyyyMMdd")).concat("/");

			File directory = new File(saveDirectory);
			if (!directory.exists()) {
				directory.mkdirs();
			}

			fileFullPath = saveDirectory.concat(fileName).concat(".txt");

			File file = new File(fileFullPath);

			out = new BufferedWriter(new FileWriter(file));

			String arrayText[] = text.split("@");

			for (int i = 0; i < arrayText.length; i++) {
				out.write(arrayText[i]);

				if (i < arrayText.length - 1) {
					out.newLine();
				}
			}

			return fileFullPath;

		} catch (Exception e) {
			log.error("makeFile Method Error");
			log.error(ExceptionUtils.getStackTrace(e));
			return "";

		} finally {
			try {
				if (out != null) {
					out.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	private void sftpUpload(List<String> fileFullPathList) {
		SFTPUtil sftpUtil = null;
		String remoteDir = "/nas/share/dsr/target/";
		String folderName = (!CommonUtil.isNullOrSpace(this.searchTicketYmd)
				? this.searchTicketYmd : DateUtil.getCurrentDate());

		try {
			sftpUtil = new SFTPUtil(ftpIp, 22, ftpUser, ftpPassword);
			sftpUtil.connect();

			for (String fileFullPath : fileFullPathList) {
				File file = new File(fileFullPath);

				if (file != null) {
					sftpUtil.upload(remoteDir, folderName, file);
				}
			}
			
			sftpUtil.disconnect();
		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();

		} finally {
			if (sftpUtil != null) {
				sftpUtil.disconnect();
			}
		}
	}
}
